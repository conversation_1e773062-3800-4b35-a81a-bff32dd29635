import {create} from 'zustand';
import {persist} from 'zustand/middleware';
import {supabase, getCurrentUser, getSession} from '@/lib/supabase';
import {Database} from '@/types/supabase';

type Profile = Database['public']['Tables']['profiles']['Row'];
type Organization = Database['public']['Tables']['organizations']['Row'];

interface AuthState {
    user: any | null;
    session: any | null;
    profile: Profile | null;
    organization: Organization | null;
    isLoading: boolean;
    isAuthenticated: boolean;

    // Initialize auth state
    initialize: () => Promise<void>;

    // Authentication methods
    login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
    signup: (
        email: string,
        password: string,
        userData: {
            firstName: string;
            lastName: string;
            mobileNumber?: string;
            organizationName: string;
        }
    ) => Promise<{ success: boolean; error?: string }>;
    logout: () => Promise<void>;

    // Verification methods
    verifyEmail: (token: string) => Promise<{ success: boolean; error?: string }>;
    verifyMobile: (code: string, mobileNumber: string) => Promise<{ success: boolean; error?: string }>;

    // Profile methods
    fetchProfile: () => Promise<void>;
    updateProfile: (data: Partial<Profile>) => Promise<{ success: boolean; error?: string }>;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set, get) => ({
            user: null,
            session: null,
            profile: null,
            organization: null,
            isLoading: true,
            isAuthenticated: false,

            initialize: async () => {
                set({isLoading: true});

                try {
                    // Get session
                    const session = await getSession();

                    if (session) {
                        const user = await getCurrentUser();

                        if (user) {
                            set({
                                user,
                                session,
                                isAuthenticated: true
                            });

                            // Fetch user profile
                            await get().fetchProfile();
                        }
                    }
                } catch (error) {
                    console.error('Error initializing auth:', error);
                } finally {
                    set({isLoading: false});
                }
            },

            login: async (email, password) => {
                try {
                    const {data, error} = await supabase.auth.signInWithPassword({
                        email,
                        password,
                    });

                    if (error) throw error;

                    set({
                        user: data.user,
                        session: data.session,
                        isAuthenticated: true
                    });

                    // Fetch user profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Login error:', error.message);
                    return {success: false, error: error.message};
                }
            },

            signup: async (email, password, userData) => {
                try {
                    // Call the atomic database function that handles everything in one transaction
                    const {data, error} = await supabase.rpc('create_user_with_profile', {
                        user_email: email,
                        user_password: password,
                        p_first_name: userData.firstName,
                        p_last_name: userData.lastName,
                        p_mobile_number: userData.mobileNumber || null,
                        p_organization_name: userData.organizationName
                    });

                    if (error) throw error;

                    // If successful, try to sign in the user
                    const {data: signInData, error: signInError} = await supabase.auth.signInWithPassword({
                        email,
                        password
                    });

                    if (signInError) {
                        // User created but couldn't sign in (probably needs email verification)
                        return {
                            success: true,
                            message: "Account created successfully! Please check your email to verify your account before signing in."
                        };
                    }

                    // Successfully signed in
                    set({
                        user: signInData.user,
                        session: signInData.session,
                        isAuthenticated: true
                    });

                    // Fetch user profile
                    await get().fetchProfile();

                    return {
                        success: true,
                        message: "Account created and signed in successfully!"
                    };

                } catch (error: any) {
                    console.error('Signup error:', error.message);
                    return {
                        success: false,
                        error: error.message || "Failed to create account. Please try again."
                    };
                }
            },

            logout: async () => {
                await supabase.auth.signOut();
                set({
                    user: null,
                    session: null,
                    profile: null,
                    organization: null,
                    isAuthenticated: false
                });
            },

            verifyEmail: async () => {
                try {
                    // This would typically be handled by Supabase automatically via email links
                    // For custom verification, you might need to implement a custom flow

                    // Update profile to mark email as verified
                    const {error} = await supabase
                        .from('profiles')
                        .update({is_email_verified: true})
                        .eq('user_id', get().user?.id);

                    if (error) throw error;

                    // Refresh profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Email verification error:', error.message);
                    return {success: false, error: error.message};
                }
            },

            verifyMobile: async (_code, mobileNumber) => {
                try {
                    // This would typically involve a third-party SMS verification service
                    // For this example, we'll simulate a successful verification

                    // Update profile to mark mobile as verified
                    const {error} = await supabase
                        .from('profiles')
                        .update({
                            is_mobile_verified: true,
                            mobile_number: mobileNumber
                        })
                        .eq('user_id', get().user?.id);

                    if (error) throw error;

                    // Refresh profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Mobile verification error:', error.message);
                    return {success: false, error: error.message};
                }
            },

            fetchProfile: async () => {
                try {
                    const {data: profile, error: profileError} = await supabase
                        .from('profiles')
                        .select('*')
                        .eq('user_id', get().user?.id)
                        .single();

                    if (profileError) throw profileError;

                    if (profile) {
                        // Fetch organization details
                        const {data: organization, error: orgError} = await supabase
                            .from('organizations')
                            .select('*')
                            .eq('uuid', profile.organization_uuid)
                            .single();

                        if (orgError) throw orgError;

                        set({profile, organization});
                    }
                } catch (error) {
                    console.error('Error fetching profile:', error);
                }
            },

            updateProfile: async (data) => {
                try {
                    const {error} = await supabase
                        .from('profiles')
                        .update(data)
                        .eq('user_id', get().user?.id);

                    if (error) throw error;

                    // Refresh profile
                    await get().fetchProfile();

                    return {success: true};
                } catch (error: any) {
                    console.error('Profile update error:', error.message);
                    return {success: false, error: error.message};
                }
            },
        }),
        {
            name: 'auth-storage',
            partialize: (state) => ({
                user: state.user,
                session: state.session,
                profile: state.profile,
                organization: state.organization,
                isAuthenticated: state.isAuthenticated
            }),
        }
    )
);
