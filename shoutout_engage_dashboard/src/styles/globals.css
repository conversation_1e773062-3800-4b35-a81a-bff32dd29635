@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #F5F4FE; /* Very light purple background */
    --foreground: #0F1729;

    --card: #FFFFFF;
    --card-foreground: #0F1729;

    --popover: #FFFFFF;
    --popover-foreground: #0F1729;

    /* New color scheme */
    --primary: #8F87F1; /* Purple */
    --primary-foreground: #FFFFFF;

    --secondary: #C68EFD; /* Light purple */
    --secondary-foreground: #FFFFFF;

    --success: #8F87F1; /* Using primary color for success */
    --success-foreground: #FFFFFF;

    --info: #C68EFD; /* Using secondary color for info */
    --info-foreground: #FFFFFF;

    --warning: #E9A5F1; /* Pink-purple */
    --warning-foreground: #FFFFFF;

    --danger: #E5484D; /* Keeping original destructive color */
    --danger-foreground: #F8FAFC;

    --light: #FED2E2; /* Light pink */
    --light-foreground: #1F2937;

    --dark: #4A4373; /* Darker version of primary */
    --dark-foreground: #FFFFFF;

    --muted: #F1F5F9;
    --muted-foreground: #64748B;

    --accent: #E9A5F1; /* Pink-purple */
    --accent-foreground: #FFFFFF;

    --destructive: #E5484D;
    --destructive-foreground: #F8FAFC;

    --border: #F5F4FE; /* Match with background color */
    --input: #E2E8F0;
    --ring: #8F87F1; /* Using primary color for ring */

    --radius: 0.5rem;
  }

  .dark {
    --background: #1E1B2D; /* Dark purple background */
    --foreground: #F8FAFC;

    --card: #282541;
    --card-foreground: #F8FAFC;

    --popover: #282541;
    --popover-foreground: #F8FAFC;

    /* Dark theme color scheme */
    --primary: #8F87F1; /* Purple */
    --primary-foreground: #FFFFFF;

    --secondary: #C68EFD; /* Light purple */
    --secondary-foreground: #FFFFFF;

    --success: #8F87F1; /* Using primary color for success */
    --success-foreground: #FFFFFF;

    --info: #C68EFD; /* Using secondary color for info */
    --info-foreground: #FFFFFF;

    --warning: #E9A5F1; /* Pink-purple */
    --warning-foreground: #FFFFFF;

    --danger: #7F1D1D; /* Keeping original destructive color */
    --danger-foreground: #F8FAFC;

    --light: #FED2E2; /* Light pink */
    --light-foreground: #1F2937;

    --dark: #4A4373; /* Darker version of primary */
    --dark-foreground: #FFFFFF;

    --muted: #3D3A5A;
    --muted-foreground: #CBD5E1;

    --accent: #E9A5F1; /* Pink-purple */
    --accent-foreground: #FFFFFF;

    --destructive: #7F1D1D;
    --destructive-foreground: #F8FAFC;

    --border: #1E1B2D; /* Match with background color */
    --input: #3D3A5A;
    --ring: #8F87F1; /* Using primary color for ring */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
