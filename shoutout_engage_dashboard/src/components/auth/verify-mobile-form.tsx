import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import { useAuthStore } from "@/store/auth-store";
import { verifyMobileSchema, type VerifyMobileFormValues } from "@/lib/validations/auth";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export function VerifyMobileForm() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const verifyMobile = useAuthStore((state) => state.verifyMobile);
  const profile = useAuthStore((state) => state.profile);

  const form = useForm<VerifyMobileFormValues>({
    resolver: zodResolver(verifyMobileSchema),
    defaultValues: {
      code: "",
      mobileNumber: profile?.mobile_number || "",
    },
  });

  async function onSubmit(data: VerifyMobileFormValues) {
    setIsLoading(true);
    
    try {
      const result = await verifyMobile(data.code, data.mobileNumber);
      
      if (result.success) {
        toast.success("Mobile number verified successfully");
        navigate("/dashboard");
      } else {
        toast.error(result.error || "Failed to verify mobile number");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Verify your mobile number
        </h1>
        <p className="text-sm text-muted-foreground">
          Enter your mobile number and the verification code we'll send
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="mobileNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mobile Number</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="+1234567890" 
                    type="tel"
                    disabled={isLoading} 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex justify-end">
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={() => toast.info("Verification code sent")}
              disabled={isLoading || !form.getValues("mobileNumber")}
            >
              Send Code
            </Button>
          </div>
          
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Verification Code</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Enter verification code" 
                    disabled={isLoading} 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Verifying..." : "Verify Mobile Number"}
          </Button>
        </form>
      </Form>

      <div className="text-center text-sm">
        <Button 
          variant="link" 
          className="p-0" 
          onClick={() => navigate("/dashboard")}
          disabled={isLoading}
        >
          Skip for now
        </Button>
      </div>
    </div>
  );
}