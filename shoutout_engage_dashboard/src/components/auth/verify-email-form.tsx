import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import { useAuthStore } from "@/store/auth-store";
import { verifyEmailSchema, type VerifyEmailFormValues } from "@/lib/validations/auth";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

export function VerifyEmailForm() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const verifyEmail = useAuthStore((state) => state.verifyEmail);
  const user = useAuthStore((state) => state.user);

  const form = useForm<VerifyEmailFormValues>({
    resolver: zod<PERSON><PERSON><PERSON>ver(verifyEmailSchema),
    defaultValues: {
      token: "",
    },
  });

  async function onSubmit(data: VerifyEmailFormValues) {
    setIsLoading(true);
    
    try {
      const result = await verifyEmail(data.token);
      
      if (result.success) {
        toast.success("Email verified successfully");
        navigate("/verify-mobile");
      } else {
        toast.error(result.error || "Failed to verify email");
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
      <div className="flex flex-col space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight">
          Verify your email
        </h1>
        <p className="text-sm text-muted-foreground">
          We've sent a verification code to {user?.email}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="token"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Verification Code</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Enter verification code" 
                    disabled={isLoading} 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Verifying..." : "Verify Email"}
          </Button>
        </form>
      </Form>

      <div className="text-center text-sm">
        Didn't receive a code?{" "}
        <Button 
          variant="link" 
          className="p-0" 
          onClick={() => toast.info("Verification code resent")}
          disabled={isLoading}
        >
          Resend code
        </Button>
      </div>
    </div>
  );
}