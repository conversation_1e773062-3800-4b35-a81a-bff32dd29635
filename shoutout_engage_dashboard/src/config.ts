export interface SupabaseConfig {
  url: string;
  anonKey: string;
}

// Supabase configuration
export const supabaseConfig:SupabaseConfig = {
  url: import.meta.env.VITE_SUPABASE_URL || '',
  anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY || '',
};

// Validate required configuration
if (!supabaseConfig.url || !supabaseConfig.anonKey) {
  console.error('Missing Supabase credentials. Please check your environment variables.');
}
