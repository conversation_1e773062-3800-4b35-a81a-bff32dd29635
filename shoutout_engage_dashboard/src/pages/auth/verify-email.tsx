import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { VerifyEmailForm } from "@/components/auth/verify-email-form";
import { useAuthStore } from "@/store/auth-store";
import logoSvg from "@/assets/images/shoutout-logo.svg";

export default function VerifyEmailPage() {
  const navigate = useNavigate();
  const { user, isLoading, profile } = useAuthStore();

  // Redirect if no user or if email is already verified
  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        navigate("/login");
      } else if (profile?.is_email_verified) {
        navigate("/verify-mobile");
      }
    }
  }, [user, profile, isLoading, navigate]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center bg-gradient-to-br from-primary/10 to-secondary/10">
      <div className="mx-auto w-full max-w-[900px] overflow-hidden rounded-xl bg-white shadow-xl">
        <div className="flex flex-col md:flex-row">
          {/* Left side - Verify Email Form */}
          <div className="w-full p-8 md:w-1/2">
            <div className="mb-6 flex flex-col space-y-2">
              <h1 className="text-2xl font-semibold tracking-tight">Verify your email</h1>
              <p className="text-sm text-muted-foreground">
                Please check your inbox for the verification code
              </p>
            </div>
            <VerifyEmailForm />
          </div>

          {/* Right side - Logo and Description */}
          <div className="hidden w-full bg-gradient-to-br from-primary to-secondary p-8 text-white md:block md:w-1/2">
            <div className="flex h-full flex-col items-center justify-center space-y-6">
              <div className="text-center">
                <img src={logoSvg} alt="ShoutOut Engage Logo" className="mx-auto mb-4" />
                <p className="mt-2 text-xl">Customer Engagement Platform</p>
              </div>
              <div className="mt-8 max-w-md text-center">
                <p className="text-light">
                  Thank you for signing up! Please verify your email to continue setting up your account and access all features.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
