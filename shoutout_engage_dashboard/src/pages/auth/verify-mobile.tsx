import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { VerifyMobileForm } from "@/components/auth/verify-mobile-form";
import { useAuthStore } from "@/store/auth-store";

export default function VerifyMobilePage() {
  const navigate = useNavigate();
  const { user, isLoading, profile } = useAuthStore();

  // Redirect if no user or if mobile is already verified
  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        navigate("/login");
      } else if (!profile?.is_email_verified) {
        navigate("/verify-email");
      } else if (profile?.is_mobile_verified) {
        navigate("/dashboard");
      }
    }
  }, [user, profile, isLoading, navigate]);

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-3xl font-bold">ShoutOut Engage</h1>
          <p className="text-sm text-muted-foreground">
            Customer Engagement Platform
          </p>
        </div>
        <div className="grid gap-6">
          <VerifyMobileForm />
        </div>
      </div>
    </div>
  );
}