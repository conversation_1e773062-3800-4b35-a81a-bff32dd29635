export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      organizations: {
        Row: {
          uuid: string
          organization_name: string
          status: 'active' | 'inactive'
          created_at: string
          updated_at: string
        }
        Insert: {
          uuid?: string
          organization_name: string
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string
        }
        Update: {
          uuid?: string
          organization_name?: string
          status?: 'active' | 'inactive'
          created_at?: string
          updated_at?: string
        }
      }
      profiles: {
        Row: {
          id: number
          user_id: string
          first_name: string
          last_name: string
          mobile_number: string | null
          is_email_verified: boolean
          is_mobile_verified: boolean
          status: 'active' | 'inactive' | 'suspended'
          organization_uuid: string
          user_role: 'ROOT' | 'SUPER_ADMIN' | 'ADMIN'
          avatar_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          user_id: string
          first_name: string
          last_name: string
          mobile_number?: string | null
          is_email_verified?: boolean
          is_mobile_verified?: boolean
          status?: 'active' | 'inactive' | 'suspended'
          organization_uuid: string
          user_role: 'ROOT' | 'SUPER_ADMIN' | 'ADMIN'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          user_id?: string
          first_name?: string
          last_name?: string
          mobile_number?: string | null
          is_email_verified?: boolean
          is_mobile_verified?: boolean
          status?: 'active' | 'inactive' | 'suspended'
          organization_uuid?: string
          user_role?: 'ROOT' | 'SUPER_ADMIN' | 'ADMIN'
          avatar_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      organization_status: 'active' | 'inactive'
      user_role: 'ROOT' | 'SUPER_ADMIN' | 'ADMIN'
      user_status: 'active' | 'inactive' | 'suspended'
    }
  }
}