-- Create enum types for organization roles and user status
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE user_role AS ENUM ('ROOT', 'SUPER_ADMIN', 'ADMIN');

-- Create profiles table that links to auth.users
CREATE TABLE IF NOT EXISTS public.profiles (
                                               id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
                                               user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    mobile_number TEXT,
    is_email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_mobile_verified BOOLEAN NOT NULL DEFAULT FALSE,
    status user_status NOT NULL DEFAULT 'inactive',
    organization_uuid UUID NOT NULL REFERENCES organizations(uuid),
    user_role user_role NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON public.profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_profiles_organization ON public.profiles(organization_uuid);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON public.profiles(status);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(user_role);
CREATE INDEX IF NOT EXISTS idx_profiles_names ON public.profiles(first_name, last_name);

-- Enable Row Level Security (RLS)
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create a trigger to automatically update the updated_at timestamp
-- Reusing the function created in the organizations migration
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments to the table
COMMENT ON TABLE public.profiles IS 'Profile information for authenticated users';
COMMENT ON COLUMN public.profiles.id IS 'Primary key for the profile';
COMMENT ON COLUMN public.profiles.user_id IS 'Foreign key to auth.users table';
COMMENT ON COLUMN public.profiles.organization_uuid IS 'Foreign key to organizations table';
COMMENT ON COLUMN public.profiles.user_role IS 'User role within the system: ROOT, SUPER_ADMIN, or ADMIN';
COMMENT ON COLUMN public.profiles.status IS 'User account status: active, inactive, or suspended';

-- Create RLS policies
-- Allow users to view their own profile
CREATE POLICY "Users can view their own profile"
    ON public.profiles
    FOR SELECT
                                                                   USING (auth.uid() = user_id);

-- Allow users to update their own profile
CREATE POLICY "Users can update their own profile"
    ON public.profiles
    FOR UPDATE
                   USING (auth.uid() = user_id);

-- Allow service role to manage all profiles
CREATE POLICY "Service role can manage all profiles"
    ON public.profiles
    USING (auth.role() = 'service_role');

-- Create a function to automatically create a profile when a new user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
INSERT INTO public.profiles (user_id, first_name, last_name, status, user_role)
VALUES (NEW.id, '', '', 'inactive', 'ADMIN');
RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to call the function when a new user is created
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();
