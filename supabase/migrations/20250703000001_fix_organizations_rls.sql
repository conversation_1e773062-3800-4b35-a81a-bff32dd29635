-- Enable Row Level Security on organizations table
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for organizations table

-- Allow authenticated users to view organizations they belong to
CREATE POLICY "Users can view their organization"
    ON public.organizations
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.organization_uuid = organizations.uuid 
            AND profiles.user_id = auth.uid()
        )
    );

-- Allow anyone to insert organizations (for signup via function)
CREATE POLICY "Allow organization creation during signup"
    ON public.organizations
    FOR INSERT
    WITH CHECK (true); -- Allow creation during signup process

-- Allow users to update their own organization
CREATE POLICY "Users can update their organization"
    ON public.organizations
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.organization_uuid = organizations.uuid 
            AND profiles.user_id = auth.uid()
            AND profiles.user_role IN ('ADMIN', 'SUPER_ADMIN', 'ROOT')
        )
    );

-- Allow service role to manage all organizations
CREATE POLICY "Service role can manage all organizations"
    ON public.organizations
    USING (auth.role() = 'service_role');

-- Grant necessary permissions to roles
GRANT SELECT, INSERT, UPDATE ON public.organizations TO authenticated;
GRANT ALL ON public.organizations TO service_role;
