CREATE OR REPLACE FUNCTION public.create_user_with_profile(
    user_email TEXT,
    user_password TEXT,
    p_first_name TEXT,
    p_last_name TEXT,
    p_organization_name TEXT,
    p_mobile_number TEXT DEFAULT NULL
) RETURNS JSONB
              LANGUAGE plpgsql
              SECURITY DEFINER
SET search_path = public, auth, pg_temp
    AS $$
DECLARE
    new_user_id UUID;
    new_org_uuid UUID;
    result JSONB;
    auth_user_data JSONB;
BEGIN
    -- Start transaction (implicit in function with SECURITY DEFINER)

    -- 1. Create user in auth.users table
    INSERT INTO auth.users (
        instance_id,
        id,
        aud,
        role,
        email,
        encrypted_password,
        email_confirmed_at,
        recovery_sent_at,
        last_sign_in_at,
        raw_app_meta_data,
        raw_user_meta_data,
        created_at,
        updated_at,
        confirmation_token,
        email_change,
        email_change_token_new,
        recovery_token
    ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        gen_random_uuid(),
        'authenticated',
        'authenticated',
        user_email,
        crypt(user_password, gen_salt('bf')),
        CASE WHEN current_setting('app.settings.auth_confirm_email', true) = 'false'
             THEN NOW()
             ELSE NULL
        END,
        NULL,
        NULL,
        '{"provider": "email", "providers": ["email"]}',
        jsonb_build_object(
            'first_name', p_first_name,
            'last_name', p_last_name,
            'mobile_number', p_mobile_number,
            'organization_name', p_organization_name
        ),
        NOW(),
        NOW(),
        CASE WHEN current_setting('app.settings.auth_confirm_email', true) = 'false'
             THEN NULL
             ELSE encode(gen_random_bytes(32), 'hex')
        END,
        '',
        '',
        ''
    )
    RETURNING id INTO new_user_id;

    -- 2. Create organization
    INSERT INTO public.organizations (
        organization_name,
        status
    ) VALUES (
        p_organization_name,
        'inactive'::organization_status -- Default status until verification
    )
    RETURNING uuid INTO new_org_uuid;

    -- 3. Create user profile
    INSERT INTO public.profiles (
        user_id,
        first_name,
        last_name,
        mobile_number,
        organization_uuid,
        user_role,
        status
    ) VALUES (
        new_user_id,
        p_first_name,
        p_last_name,
        p_mobile_number,
        new_org_uuid,
        'ADMIN'::user_role,
        'inactive'::user_status
    );

    -- 4. Create identity record for email authentication
    INSERT INTO auth.identities (
        id,
        user_id,
        identity_data,
        provider,
        last_sign_in_at,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        new_user_id,
        jsonb_build_object(
            'sub', new_user_id::text,
            'email', user_email
        ),
        'email',
        NOW(),
        NOW(),
        NOW()
    );

    -- 5. Return result as JSONB
    result := jsonb_build_object(
        'user_id', new_user_id,
        'organization_id', new_org_uuid,
        'email', user_email,
        'success', true,
        'message', 'User, organization, and profile created successfully'
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- All operations will be rolled back automatically due to transaction failure
        RAISE EXCEPTION 'Transaction failed - all changes rolled back. Error: %', SQLERRM;
END;
$$;

-- Revoke public execution for security
REVOKE EXECUTE ON FUNCTION public.create_user_with_profile FROM PUBLIC;

-- Grant execution to Supabase roles (anon can call this for signup)
GRANT EXECUTE ON FUNCTION public.create_user_with_profile TO anon;
GRANT EXECUTE ON FUNCTION public.create_user_with_profile TO authenticator;
GRANT EXECUTE ON FUNCTION public.create_user_with_profile TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_with_profile TO service_role;

-- Grant necessary permissions for the function to work
GRANT USAGE ON SCHEMA auth TO authenticator;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant table permissions needed for the function
GRANT INSERT ON auth.users TO authenticator;
GRANT INSERT ON auth.identities TO authenticator;
GRANT INSERT ON public.organizations TO anon;
GRANT INSERT ON public.profiles TO anon;

-- Document the function
COMMENT ON FUNCTION public.create_user_with_profile IS
'Creates user account, profile and organization in a single atomic transaction. All operations are rolled back if any step fails.';